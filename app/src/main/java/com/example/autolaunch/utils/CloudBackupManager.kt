package com.example.autolaunch.utils

import android.content.Context
import android.util.Log
import com.example.autolaunch.model.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlinx.coroutines.flow.first

/**
 * 雲端備份管理器
 * 整合本地備份和Google Drive雲端備份功能
 */
class CloudBackupManager(private val context: Context) {
    
    companion object {
        private const val TAG = "CloudBackupManager"
    }
    
    private val backupManager = BackupManager(context)
    private val googleDriveManager = GoogleDriveManager(context)
    
    /**
     * 初始化Google Drive服務
     */
    suspend fun initializeGoogleDrive(): Boolean {
        return googleDriveManager.initializeDriveService()
    }
    
    /**
     * 檢查是否已登錄Google帳戶
     */
    fun isGoogleSignedIn(): Bo<PERSON>an {
        return googleDriveManager.isSignedIn()
    }
    
    /**
     * 獲取Google Sign-In客戶端
     */
    fun getGoogleSignInClient() = googleDriveManager.initializeGoogleSignIn()
    
    /**
     * 創建本地備份
     */
    suspend fun createLocalBackup(): BackupResult {
        return backupManager.createLocalBackup()
    }
    
    /**
     * 創建雲端備份
     */
    suspend fun createCloudBackup(): BackupResult = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Starting cloud backup creation")
            
            if (!isGoogleSignedIn()) {
                return@withContext BackupResult(
                    success = false,
                    message = "請先登錄Google帳戶"
                )
            }
            
            if (!initializeGoogleDrive()) {
                return@withContext BackupResult(
                    success = false,
                    message = "無法連接到Google Drive"
                )
            }
            
            // 獲取所有排程數據
            val repository = ScheduleRepository(context)
            val schedules = repository.getAllSchedules().first()
            
            if (schedules.isEmpty()) {
                return@withContext BackupResult(
                    success = false,
                    message = "沒有排程數據可以備份"
                )
            }
            
            // 創建備份數據
            val backupData = backupManager.createBackupData(schedules)
            val jsonString = backupManager.gson.toJson(backupData)
            
            // 上傳到Google Drive
            val driveResult = googleDriveManager.uploadBackup(
                backupData.getSuggestedFileName(),
                jsonString
            )
            
            if (driveResult.success) {
                Log.d(TAG, "Cloud backup created successfully")
                BackupResult(
                    success = true,
                    message = "雲端備份創建成功",
                    filePath = driveResult.fileId,
                    fileSize = driveResult.fileSize,
                    scheduleCount = schedules.size
                )
            } else {
                BackupResult(
                    success = false,
                    message = driveResult.message,
                    error = driveResult.error
                )
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to create cloud backup", e)
            BackupResult(
                success = false,
                message = "雲端備份創建失敗：${e.message}",
                error = e
            )
        }
    }
    
    /**
     * 從本地文件恢復
     */
    suspend fun restoreFromLocalFile(filePath: String): RestoreResult {
        return backupManager.restoreFromLocalFile(filePath)
    }
    
    /**
     * 從Uri恢復
     */
    suspend fun restoreFromUri(uri: android.net.Uri): RestoreResult {
        return backupManager.restoreFromUri(context, uri)
    }
    
    /**
     * 從雲端文件恢復
     */
    suspend fun restoreFromCloudFile(fileId: String): RestoreResult = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Starting restore from cloud file: $fileId")
            
            if (!isGoogleSignedIn()) {
                return@withContext RestoreResult(
                    success = false,
                    message = "請先登錄Google帳戶"
                )
            }
            
            if (!initializeGoogleDrive()) {
                return@withContext RestoreResult(
                    success = false,
                    message = "無法連接到Google Drive"
                )
            }
            
            // 從Google Drive下載文件
            val driveResult = googleDriveManager.downloadBackup(fileId)
            if (!driveResult.success) {
                return@withContext RestoreResult(
                    success = false,
                    message = driveResult.message
                )
            }
            
            // 執行恢復
            val content = driveResult.content ?: return@withContext RestoreResult(
                success = false,
                message = "下載的文件內容為空"
            )
            
            backupManager.restoreFromJson(content)
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to restore from cloud file", e)
            RestoreResult(
                success = false,
                message = "從雲端恢復失敗：${e.message}",
                errorCount = 1,
                errors = listOf(e.message ?: "未知錯誤")
            )
        }
    }
    
    /**
     * 保存備份到Uri
     */
    suspend fun saveBackupToUri(uri: android.net.Uri): BackupResult {
        return backupManager.saveBackupToUri(context, uri)
    }
    
    /**
     * 獲取本地備份文件列表
     */
    suspend fun getLocalBackupFiles(): List<java.io.File> {
        return backupManager.getLocalBackupFiles()
    }
    
    /**
     * 獲取雲端備份文件列表
     */
    suspend fun getCloudBackupFiles(): List<DriveFileInfo> = withContext(Dispatchers.IO) {
        try {
            if (!isGoogleSignedIn()) {
                return@withContext emptyList()
            }
            
            if (!initializeGoogleDrive()) {
                return@withContext emptyList()
            }
            
            googleDriveManager.listBackupFiles()
        } catch (e: Exception) {
            Log.e(TAG, "Failed to get cloud backup files", e)
            emptyList()
        }
    }
    
    /**
     * 刪除本地備份文件
     */
    suspend fun deleteLocalBackupFile(file: java.io.File): Boolean {
        return backupManager.deleteLocalBackupFile(file)
    }
    
    /**
     * 刪除雲端備份文件
     */
    suspend fun deleteCloudBackupFile(fileId: String): DriveOperationResult = withContext(Dispatchers.IO) {
        try {
            if (!isGoogleSignedIn()) {
                return@withContext DriveOperationResult(
                    success = false,
                    message = "請先登錄Google帳戶"
                )
            }
            
            if (!initializeGoogleDrive()) {
                return@withContext DriveOperationResult(
                    success = false,
                    message = "無法連接到Google Drive"
                )
            }
            
            googleDriveManager.deleteBackupFile(fileId)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to delete cloud backup file", e)
            DriveOperationResult(
                success = false,
                message = "刪除雲端文件失敗：${e.message}",
                error = e
            )
        }
    }
    
    /**
     * 獲取本地備份文件信息
     */
    suspend fun getLocalBackupFileInfo(file: java.io.File): BackupFileInfo? {
        return backupManager.getBackupFileInfo(file)
    }
    
    /**
     * 登出Google帳戶
     */
    suspend fun signOutGoogle(): Boolean {
        return try {
            googleDriveManager.signOut()
            true
        } catch (e: Exception) {
            Log.e(TAG, "Sign out failed in CloudBackupManager", e)
            false
        }
    }
    
    /**
     * 獲取當前Google帳戶信息
     */
    fun getCurrentGoogleAccount() = googleDriveManager.getCurrentAccount()
    
    /**
     * 同步備份（創建本地和雲端備份）
     */
    suspend fun syncBackup(): SyncBackupResult = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Starting sync backup")
            
            val localResult = createLocalBackup()
            val cloudResult = if (isGoogleSignedIn()) {
                createCloudBackup()
            } else {
                BackupResult(success = false, message = "未登錄Google帳戶")
            }
            
            SyncBackupResult(
                localBackupResult = localResult,
                cloudBackupResult = cloudResult,
                success = localResult.success || cloudResult.success
            )
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to sync backup", e)
            SyncBackupResult(
                localBackupResult = BackupResult(success = false, message = "同步失敗：${e.message}", error = e),
                cloudBackupResult = BackupResult(success = false, message = "同步失敗：${e.message}", error = e),
                success = false
            )
        }
    }
}

/**
 * 同步備份結果
 */
data class SyncBackupResult(
    val localBackupResult: BackupResult,
    val cloudBackupResult: BackupResult,
    val success: Boolean
) {
    fun getMessage(): String {
        return when {
            localBackupResult.success && cloudBackupResult.success -> 
                "本地和雲端備份都創建成功"
            localBackupResult.success && !cloudBackupResult.success -> 
                "本地備份成功，雲端備份失敗：${cloudBackupResult.message}"
            !localBackupResult.success && cloudBackupResult.success -> 
                "雲端備份成功，本地備份失敗：${localBackupResult.message}"
            else -> 
                "本地和雲端備份都失敗"
        }
    }
}
