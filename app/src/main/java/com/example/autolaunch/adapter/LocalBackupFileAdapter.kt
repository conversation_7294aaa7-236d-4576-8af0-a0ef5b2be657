package com.example.autolaunch.adapter

import android.text.format.DateUtils
import android.text.format.Formatter
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.example.autolaunch.databinding.ItemBackupFileBinding
import com.example.autolaunch.model.BackupFileInfo
import java.io.File

/**
 * 本地備份文件適配器
 */
class LocalBackupFileAdapter(
    private val onFileSelected: (File, BackupFileInfo?) -> Unit,
    private val onFileDeleted: (File) -> Unit
) : RecyclerView.Adapter<LocalBackupFileAdapter.ViewHolder>() {
    
    private var files: List<File> = emptyList()
    private var fileInfos: List<BackupFileInfo?> = emptyList()
    
    fun updateFiles(newFiles: List<File>, newFileInfos: List<BackupFileInfo?>) {
        files = newFiles
        fileInfos = newFileInfos
        notifyDataSetChanged()
    }
    
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val binding = ItemBackupFileBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return ViewHolder(binding)
    }
    
    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val file = files[position]
        val fileInfo = if (position < fileInfos.size) fileInfos[position] else null
        
        holder.bind(file, fileInfo, onFileSelected, onFileDeleted)
    }
    
    override fun getItemCount() = files.size
    
    class ViewHolder(private val binding: ItemBackupFileBinding) : RecyclerView.ViewHolder(binding.root) {
        
        fun bind(
            file: File,
            fileInfo: BackupFileInfo?,
            onFileSelected: (File, BackupFileInfo?) -> Unit,
            onFileDeleted: (File) -> Unit
        ) {
            // 設定文件名
            binding.tvFileName.text = file.name
            
            // 設定文件信息
            if (fileInfo != null && fileInfo.isValid) {
                binding.tvFileInfo.text = "${fileInfo.scheduleCount} 個排程"
                binding.tvFileSize.text = Formatter.formatFileSize(binding.root.context, fileInfo.fileSize)
                binding.tvCreatedTime.text = DateUtils.getRelativeTimeSpanString(
                    fileInfo.createdTime,
                    System.currentTimeMillis(),
                    DateUtils.MINUTE_IN_MILLIS
                )
            } else {
                binding.tvFileInfo.text = "文件信息無效"
                binding.tvFileSize.text = Formatter.formatFileSize(binding.root.context, file.length())
                binding.tvCreatedTime.text = DateUtils.getRelativeTimeSpanString(
                    file.lastModified(),
                    System.currentTimeMillis(),
                    DateUtils.MINUTE_IN_MILLIS
                )
            }
            
            // 設定按鈕點擊事件
            binding.btnRestore.setOnClickListener {
                onFileSelected(file, fileInfo)
            }
            
            binding.btnDelete.setOnClickListener {
                onFileDeleted(file)
            }
            
            // 如果文件信息無效，禁用恢復按鈕
            binding.btnRestore.isEnabled = fileInfo?.isValid == true
        }
    }
}
