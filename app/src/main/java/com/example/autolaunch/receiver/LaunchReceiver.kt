package com.example.autolaunch.receiver

import android.app.NotificationChannel
import android.app.NotificationManager
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.os.PowerManager
import android.util.Log
import androidx.core.app.NotificationCompat
import com.example.autolaunch.AlarmManagerService
import com.example.autolaunch.R
import com.example.autolaunch.model.AppDatabase
import com.example.autolaunch.model.RepeatMode
import com.example.autolaunch.model.ScheduleType
import com.example.autolaunch.service.ScheduleService
import com.example.autolaunch.utils.SystemLogManager
import com.example.autolaunch.utils.UrlUtils
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlinx.coroutines.flow.first

/**
 * 應用程式啟動廣播接收器
 * 用於接收 AlarmManager 觸發的廣播並啟動目標應用程式
 */
class LaunchReceiver : BroadcastReceiver() {
    
    companion object {
        private const val TAG = "LaunchReceiver"
        const val ACTION_LAUNCH_APP = "com.example.autolaunch.action.LAUNCH_APP"
        const val ACTION_LAUNCH_URL = "com.example.autolaunch.action.LAUNCH_URL"
        const val EXTRA_PACKAGE_NAME = "extra_package_name"
        const val EXTRA_URL = "extra_url"
        const val EXTRA_SCHEDULE_ID = "extra_schedule_id"
        private const val WAKE_LOCK_TIMEOUT = 10_000L // 10 seconds
        private const val NOTIFICATION_CHANNEL_ID = "app_launch_failures"
        private const val NOTIFICATION_ID = 1001
    }
    
    override fun onReceive(context: Context, intent: Intent) {
        Log.i(TAG, "=== LaunchReceiver triggered ===")
        Log.i(TAG, "Received broadcast: ${intent.action}")
        Log.i(TAG, "Intent extras: ${intent.extras?.keySet()?.joinToString()}")

        // 確保前台服務正在運行
        try {
            ScheduleService.startService(context)
            Log.i(TAG, "Foreground service started from LaunchReceiver")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to start schedule service from LaunchReceiver", e)
        }

        // 獲取 WakeLock 確保在啟動應用程式過程中 CPU 不會休眠
        val powerManager = context.getSystemService(Context.POWER_SERVICE) as PowerManager
        val wakeLock = powerManager.newWakeLock(
            PowerManager.PARTIAL_WAKE_LOCK,
            "AutoLaunch::LaunchReceiver"
        )

        try {
            wakeLock.acquire(WAKE_LOCK_TIMEOUT)
            Log.i(TAG, "WakeLock acquired for ${WAKE_LOCK_TIMEOUT}ms")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to acquire WakeLock", e)
            // 如果無法獲取 WakeLock，仍然繼續執行
        }
        
        when (intent.action) {
            ACTION_LAUNCH_APP -> {
                val packageName = intent.getStringExtra(EXTRA_PACKAGE_NAME)
                val scheduleId = intent.getLongExtra(EXTRA_SCHEDULE_ID, -1)

                Log.i(TAG, "Launching app: $packageName, Schedule ID: $scheduleId")

                if (!packageName.isNullOrEmpty() && scheduleId != -1L) {
                    // 使用 goAsync() 確保接收器可以執行後台操作
                    val pendingResult = goAsync()

                    CoroutineScope(Dispatchers.IO).launch {
                        try {
                            launchApp(context, packageName, scheduleId)
                        } catch (e: Exception) {
                            Log.e(TAG, "Error launching app: $packageName", e)
                        } finally {
                            pendingResult.finish()
                            safeReleaseWakeLock(wakeLock)
                        }
                    }
                } else {
                    Log.e(TAG, "Invalid parameters: packageName=$packageName, scheduleId=$scheduleId")
                    safeReleaseWakeLock(wakeLock)
                }
            }
            ACTION_LAUNCH_URL -> {
                val url = intent.getStringExtra(EXTRA_URL)
                val scheduleId = intent.getLongExtra(EXTRA_SCHEDULE_ID, -1)

                Log.i(TAG, "Launching URL: $url, Schedule ID: $scheduleId")

                if (!url.isNullOrEmpty() && scheduleId != -1L) {
                    // 使用 goAsync() 確保接收器可以執行後台操作
                    val pendingResult = goAsync()

                    CoroutineScope(Dispatchers.IO).launch {
                        try {
                            launchUrl(context, url, scheduleId)
                        } catch (e: Exception) {
                            Log.e(TAG, "Error launching URL: $url", e)
                        } finally {
                            pendingResult.finish()
                            safeReleaseWakeLock(wakeLock)
                        }
                    }
                } else {
                    Log.e(TAG, "Invalid parameters: url=$url, scheduleId=$scheduleId")
                    safeReleaseWakeLock(wakeLock)
                }
            }
            else -> {
                safeReleaseWakeLock(wakeLock)
            }
        }
    }
    
    private suspend fun launchApp(context: Context, packageName: String, scheduleId: Long) {
        try {
            // 檢查應用程式是否存在
            val isAppInstalled = try {
                context.packageManager.getApplicationInfo(packageName, 0)
                true
            } catch (e: PackageManager.NameNotFoundException) {
                false
            }
            
            if (!isAppInstalled) {
                Log.e(TAG, "App not installed: $packageName")
                logScheduleExecution(context, scheduleId, packageName, false, "應用程式已被卸載")
                handleAppLaunchFailure(context, packageName, scheduleId, "應用程式已被卸載")
                return
            }
            
            // 切換到主線程啟動應用程式
            val launchSuccess = withContext(Dispatchers.Main) {
                try {
                    val intent = context.packageManager.getLaunchIntentForPackage(packageName)
                    if (intent != null) {
                        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                        context.startActivity(intent)
                        Log.i(TAG, "Successfully launched app: $packageName")
                        true
                    } else {
                        Log.e(TAG, "Cannot find launch intent for package: $packageName")
                        false
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "Exception while launching app: $packageName", e)
                    false
                }
            }
            
            if (launchSuccess) {
                // 記錄排程執行成功日志
                logScheduleExecution(context, scheduleId, packageName, true)

                // 更新最後執行時間
                updateLastExecutionTime(context, scheduleId)

                // 設定下次鬧鐘（如果是重複排程）
                scheduleNextAlarm(context, scheduleId)
            } else {
                // 記錄排程執行失敗日志
                logScheduleExecution(context, scheduleId, packageName, false, "應用程式啟動失敗")
                handleAppLaunchFailure(context, packageName, scheduleId, "應用程式啟動失敗")
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to launch app: $packageName", e)
            logScheduleExecution(context, scheduleId, packageName, false, "應用程式啟動異常：${e.message}")
            handleAppLaunchFailure(context, packageName, scheduleId, "應用程式啟動異常")
        }
    }

    private suspend fun launchUrl(context: Context, url: String, scheduleId: Long) {
        try {
            Log.i(TAG, "Attempting to launch URL: $url")

            // 切換到主線程開啟 URL
            val launchSuccess = withContext(Dispatchers.Main) {
                try {
                    val intent = Intent(Intent.ACTION_VIEW, Uri.parse(url))
                    intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                    context.startActivity(intent)
                    Log.i(TAG, "Successfully launched URL: $url")
                    true
                } catch (e: Exception) {
                    Log.e(TAG, "Exception while launching URL: $url", e)
                    false
                }
            }

            if (launchSuccess) {
                // 記錄排程執行成功日志
                logScheduleExecution(context, scheduleId, url, true)

                // 更新最後執行時間
                updateLastExecutionTime(context, scheduleId)

                // 設定下次鬧鐘（如果是重複排程）
                scheduleNextAlarm(context, scheduleId)
            } else {
                // 記錄排程執行失敗日志
                logScheduleExecution(context, scheduleId, url, false, "網址開啟失敗")
                handleUrlLaunchFailure(context, url, scheduleId, "網址開啟失敗")
            }

        } catch (e: Exception) {
            Log.e(TAG, "Failed to launch URL: $url", e)
            logScheduleExecution(context, scheduleId, url, false, "網址開啟異常：${e.message}")
            handleUrlLaunchFailure(context, url, scheduleId, "網址開啟異常")
        }
    }

    private suspend fun updateLastExecutionTime(context: Context, scheduleId: Long) {
        try {
            val database = AppDatabase.getDatabase(context)
            val scheduleDao = database.scheduleDao()
            val currentTime = System.currentTimeMillis()
            
            val result = scheduleDao.updateLastExecutedTime(scheduleId, currentTime)
            if (result > 0) {
                Log.i(TAG, "Updated last execution time for schedule $scheduleId")
            } else {
                Log.w(TAG, "Failed to update last execution time for schedule $scheduleId")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error updating last execution time for schedule $scheduleId", e)
        }
    }

    private suspend fun logScheduleExecution(
        context: Context,
        scheduleId: Long,
        target: String,
        success: Boolean,
        errorMessage: String? = null
    ) {
        try {
            val database = AppDatabase.getDatabase(context)
            val scheduleDao = database.scheduleDao()

            // 獲取排程信息以記錄日志
            val schedule = scheduleDao.getScheduleById(scheduleId).first()
            if (schedule != null) {
                val scheduleName = schedule.getDisplayName()

                SystemLogManager.logScheduleExecuted(
                    context = context,
                    scheduleId = scheduleId,
                    scheduleName = scheduleName,
                    success = success,
                    details = errorMessage
                )
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to log schedule execution for schedule $scheduleId", e)
        }
    }
    
    private suspend fun scheduleNextAlarm(context: Context, scheduleId: Long) {
        try {
            val database = AppDatabase.getDatabase(context)
            val scheduleDao = database.scheduleDao()

            // 獲取排程資訊（只獲取一次，不持續監聽）
            val schedule = scheduleDao.getScheduleById(scheduleId).first()
            if (schedule != null && schedule.isEnabled) {
                // 如果是重複排程，設定下次鬧鐘
                if (schedule.getRepeatModeEnum() != RepeatMode.ONCE) {
                    val alarmService = AlarmManagerService(context)
                    alarmService.setAlarm(schedule)
                    Log.i(TAG, "Next alarm scheduled for recurring schedule $scheduleId")
                } else {
                    // 單次排程執行後自動禁用
                    scheduleDao.updateEnabledStatus(scheduleId, false)
                    Log.i(TAG, "Single execution schedule $scheduleId disabled after execution")
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error scheduling next alarm for schedule $scheduleId", e)
        }
    }
    
    private suspend fun handleAppLaunchFailure(
        context: Context, 
        packageName: String, 
        scheduleId: Long, 
        reason: String
    ) {
        try {
            val database = AppDatabase.getDatabase(context)
            val scheduleDao = database.scheduleDao()
            
            // 獲取排程資訊
            scheduleDao.getScheduleById(scheduleId).collect { schedule ->
                if (schedule != null) {
                    // 禁用排程
                    scheduleDao.updateEnabledStatus(scheduleId, false)
                    
                    // 取消鬧鐘
                    val alarmService = AlarmManagerService(context)
                    alarmService.cancelAlarm(scheduleId)
                    
                    // 發送通知
                    sendFailureNotification(context, schedule.getDisplayName(), reason)
                    
                    Log.w(TAG, "Disabled schedule $scheduleId due to failure: $reason")
                }
                return@collect // 只取第一個值
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error handling app launch failure for schedule $scheduleId", e)
        }
    }
    
    private fun sendFailureNotification(context: Context, appName: String, reason: String) {
        val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        
        // 創建通知通道（Android 8.0+）
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                NOTIFICATION_CHANNEL_ID,
                "應用程式啟動失敗",
                NotificationManager.IMPORTANCE_DEFAULT
            ).apply {
                description = "當排程的應用程式無法啟動時發送通知"
            }
            notificationManager.createNotificationChannel(channel)
        }
        
        // 創建通知
        val notification = NotificationCompat.Builder(context, NOTIFICATION_CHANNEL_ID)
            .setSmallIcon(R.drawable.ic_schedule_24)
            .setContentTitle("排程執行失敗")
            .setContentText("「$appName」的排程已被禁用：$reason")
            .setStyle(NotificationCompat.BigTextStyle()
                .bigText("「$appName」的排程已被自動禁用。\n原因：$reason\n\n請檢查該應用程式是否已被卸載或需要更新排程設定。"))
            .setPriority(NotificationCompat.PRIORITY_DEFAULT)
            .setAutoCancel(true)
            .build()
        
        notificationManager.notify(NOTIFICATION_ID, notification)
    }

    private suspend fun handleUrlLaunchFailure(
        context: Context,
        url: String,
        scheduleId: Long,
        reason: String
    ) {
        try {
            val database = AppDatabase.getDatabase(context)
            val scheduleDao = database.scheduleDao()

            // 獲取排程資訊
            scheduleDao.getScheduleById(scheduleId).collect { schedule ->
                if (schedule != null) {
                    // 禁用排程
                    scheduleDao.updateEnabledStatus(scheduleId, false)

                    // 取消鬧鐘
                    val alarmService = AlarmManagerService(context)
                    alarmService.cancelAlarm(scheduleId)

                    // 發送通知
                    sendFailureNotification(context, schedule.getDisplayName(), reason)

                    Log.w(TAG, "Disabled schedule $scheduleId due to failure: $reason")
                }
                return@collect // 只取第一個值
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error handling URL launch failure for schedule $scheduleId", e)
        }
    }

    /**
     * 安全釋放 WakeLock，防止記憶體洩漏
     */
    private fun safeReleaseWakeLock(wakeLock: PowerManager.WakeLock) {
        try {
            if (wakeLock.isHeld) {
                wakeLock.release()
                Log.d(TAG, "WakeLock released safely")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error releasing WakeLock", e)
        }
    }
}