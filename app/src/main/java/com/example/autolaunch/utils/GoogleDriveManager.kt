package com.example.autolaunch.utils

import android.content.Context
import android.util.Log
import com.google.android.gms.auth.api.signin.GoogleSignIn
import com.google.android.gms.auth.api.signin.GoogleSignInAccount
import com.google.android.gms.auth.api.signin.GoogleSignInClient
import com.google.android.gms.auth.api.signin.GoogleSignInOptions
import com.google.android.gms.common.api.Scope
import com.google.api.client.http.javanet.NetHttpTransport
import com.google.api.client.googleapis.extensions.android.gms.auth.GoogleAccountCredential
import com.google.api.client.http.ByteArrayContent
import com.google.api.client.json.gson.GsonFactory
import com.google.api.services.drive.Drive
import com.google.api.services.drive.DriveScopes
import com.google.api.services.drive.model.File
import com.google.api.services.drive.model.FileList
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.tasks.await
import kotlinx.coroutines.withContext
import java.io.ByteArrayOutputStream
import java.io.IOException
import java.util.*

/**
 * Google Drive 管理器
 * 負責與Google Drive API的交互操作
 */
class GoogleDriveManager(private val context: Context) {
    
    companion object {
        private const val TAG = "GoogleDriveManager"
        private const val BACKUP_FOLDER_NAME = "AutoLaunch Backups"
        private const val APPLICATION_NAME = "AutoLaunch"
    }
    
    private var driveService: Drive? = null
    private var googleSignInClient: GoogleSignInClient? = null
    
    /**
     * 初始化Google Sign-In客戶端
     */
    fun initializeGoogleSignIn(): GoogleSignInClient {
        val gso = GoogleSignInOptions.Builder(GoogleSignInOptions.DEFAULT_SIGN_IN)
            .requestEmail()
            .requestScopes(Scope(DriveScopes.DRIVE_FILE))
            .build()
        
        // 每次都重新獲取客戶端，確保狀態最新
        googleSignInClient = null
        googleSignInClient = GoogleSignIn.getClient(context, gso)
        return googleSignInClient!!
    }
    
    /**
     * 檢查是否已經登錄Google帳戶
     */
    fun isSignedIn(): Boolean {
        val account = GoogleSignIn.getLastSignedInAccount(context)
        return account != null && GoogleSignIn.hasPermissions(account, Scope(DriveScopes.DRIVE_FILE))
    }
    
    /**
     * 獲取當前登錄的Google帳戶
     */
    fun getCurrentAccount(): GoogleSignInAccount? {
        return GoogleSignIn.getLastSignedInAccount(context)
    }
    
    /**
     * 初始化Drive服務
     */
    suspend fun initializeDriveService(): Boolean = withContext(Dispatchers.IO) {
        try {
            val account = getCurrentAccount()
            if (account == null) {
                Log.e(TAG, "No Google account signed in")
                return@withContext false
            }

            val credential = GoogleAccountCredential.usingOAuth2(
                context, 
                setOf(DriveScopes.DRIVE_FILE)
            )
            credential.selectedAccount = account.account

            driveService = Drive.Builder(
                NetHttpTransport(),
                GsonFactory(),
                credential
            )
                .setApplicationName(APPLICATION_NAME)
                .build()

            true
        } catch (e: Exception) {
            Log.e(TAG, "Failed to initialize Drive service", e)
            false
        }
    }
    
    /**
     * 創建或獲取備份文件夾
     */
    private suspend fun getOrCreateBackupFolder(): String? = withContext(Dispatchers.IO) {
        try {
            val service = driveService ?: return@withContext null

            // 搜索現有的備份文件夾
            val query = "name='$BACKUP_FOLDER_NAME' and mimeType='application/vnd.google-apps.folder' and trashed=false"
            val result = service.files().list()
                .setQ(query)
                .setSpaces("drive")
                .setFields("files(id, name)")
                .execute()

            val files = result.files
            if (files != null && files.isNotEmpty()) {
                // 文件夾已存在
                val folderId = files[0].id
                Log.d(TAG, "Found existing backup folder: $folderId")
                return@withContext folderId
            }

            // 創建新的備份文件夾
            val folderMetadata = File().apply {
                name = BACKUP_FOLDER_NAME
                mimeType = "application/vnd.google-apps.folder"
            }

            val folder = service.files().create(folderMetadata)
                .setFields("id")
                .execute()

            Log.d(TAG, "Created new backup folder: ${folder.id}")
            return@withContext folder.id

        } catch (e: Exception) {
            Log.e(TAG, "Failed to get or create backup folder", e)
            null
        }
    }
    
    /**
     * 上傳備份文件到Google Drive
     */
    suspend fun uploadBackup(fileName: String, content: String): DriveOperationResult = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Starting upload to Google Drive: $fileName (${content.length} characters)")

            val service = driveService
            if (service == null) {
                Log.e(TAG, "Drive service not initialized")
                return@withContext DriveOperationResult(
                    success = false,
                    message = "Google Drive 服務未初始化，請重新登入"
                )
            }

            // 獲取或創建備份文件夾
            val folderId = getOrCreateBackupFolder()
            if (folderId == null) {
                return@withContext DriveOperationResult(
                    success = false,
                    message = "無法創建備份文件夾"
                )
            }

            // 創建文件元數據
            val fileMetadata = File().apply {
                name = fileName
                parents = listOf(folderId)
                mimeType = "application/json"
            }

            // 創建文件內容
            val mediaContent = ByteArrayContent.fromString("application/json", content)

            // 上傳文件
            val file = service.files().create(fileMetadata, mediaContent)
                .setFields("id,name,size,createdTime")
                .execute()

            Log.d(TAG, "Upload successful: ${file.name} (ID: ${file.id})")

            DriveOperationResult(
                success = true,
                message = "雲端備份創建成功",
                fileId = file.id,
                fileName = file.name,
                fileSize = file.size?.toLong() ?: 0L
            )

        } catch (e: Exception) {
            Log.e(TAG, "Failed to upload backup", e)
            DriveOperationResult(
                success = false,
                message = "上傳失敗：${e.message}",
                error = e
            )
        }
    }
    
    /**
     * 從Google Drive下載備份文件
     */
    suspend fun downloadBackup(fileId: String): DriveOperationResult = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Starting download from Google Drive: $fileId")

            val service = driveService
            if (service == null) {
                Log.e(TAG, "Drive service not initialized")
                return@withContext DriveOperationResult(
                    success = false,
                    message = "Google Drive 服務未初始化，請重新登入"
                )
            }

            // 下載文件內容
            val outputStream = ByteArrayOutputStream()
            service.files().get(fileId).executeMediaAndDownloadTo(outputStream)

            val content = outputStream.toString("UTF-8")
            Log.d(TAG, "Download successful: ${content.length} characters")

            DriveOperationResult(
                success = true,
                message = "雲端文件下載成功",
                content = content
            )

        } catch (e: Exception) {
            Log.e(TAG, "Failed to download backup", e)
            DriveOperationResult(
                success = false,
                message = "下載失敗：${e.message}",
                error = e
            )
        }
    }
    
    /**
     * 列出Google Drive中的備份文件
     */
    suspend fun listBackupFiles(): List<DriveFileInfo> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Listing backup files from Google Drive")

            val service = driveService
            if (service == null) {
                Log.e(TAG, "Drive service not initialized")
                return@withContext emptyList()
            }

            // 獲取備份文件夾ID
            val folderId = getOrCreateBackupFolder()
            if (folderId == null) {
                Log.e(TAG, "Cannot access backup folder")
                return@withContext emptyList()
            }

            // 搜索備份文件夾中的JSON文件
            val query = "'$folderId' in parents and mimeType='application/json' and trashed=false"
            val result = service.files().list()
                .setQ(query)
                .setOrderBy("createdTime desc")
                .setFields("files(id,name,size,createdTime,modifiedTime)")
                .execute()

            val files = result.files ?: return@withContext emptyList()

            val driveFiles = files.map { file ->
                DriveFileInfo(
                    id = file.id,
                    name = file.name,
                    size = file.size?.toLong() ?: 0L,
                    createdTime = file.createdTime?.value ?: 0L,
                    modifiedTime = file.modifiedTime?.value ?: 0L
                )
            }

            Log.d(TAG, "Found ${driveFiles.size} backup files")
            return@withContext driveFiles

        } catch (e: Exception) {
            Log.e(TAG, "Failed to list backup files", e)
            emptyList()
        }
    }
    
    /**
     * 刪除Google Drive中的備份文件
     */
    suspend fun deleteBackupFile(fileId: String): DriveOperationResult = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Deleting backup file from Google Drive: $fileId")

            val service = driveService
            if (service == null) {
                Log.e(TAG, "Drive service not initialized")
                return@withContext DriveOperationResult(
                    success = false,
                    message = "Google Drive 服務未初始化，請重新登入"
                )
            }

            // 刪除文件
            service.files().delete(fileId).execute()

            Log.d(TAG, "File deleted successfully: $fileId")
            DriveOperationResult(
                success = true,
                message = "雲端備份文件刪除成功"
            )

        } catch (e: Exception) {
            Log.e(TAG, "Failed to delete backup file", e)
            DriveOperationResult(
                success = false,
                message = "刪除失敗：${e.message}",
                error = e
            )
        }
    }
    
    /**
     * 登出Google帳戶
     */
    suspend fun signOut() {
        try {
            googleSignInClient?.signOut()?.await()
            driveService = null
            Log.d(TAG, "Google account signed out successfully")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to sign out", e)
            // 可選：向上拋出異常或返回一個結果對象
            throw e
        }
    }
}

/**
 * Drive操作結果
 */
data class DriveOperationResult(
    val success: Boolean,
    val message: String,
    val fileId: String? = null,
    val fileName: String? = null,
    val fileSize: Long = 0L,
    val content: String? = null,
    val error: Throwable? = null
)

/**
 * Drive文件信息
 */
data class DriveFileInfo(
    val id: String,
    val name: String,
    val size: Long,
    val createdTime: Long,
    val modifiedTime: Long
) {
    /**
     * 獲取格式化的文件大小
     */
    fun getFormattedSize(): String {
        return FileUtils.formatFileSize(size)
    }
    
    /**
     * 獲取格式化的創建時間
     */
    fun getFormattedCreatedTime(): String {
        val date = Date(createdTime)
        return android.text.format.DateFormat.format("yyyy-MM-dd HH:mm:ss", date).toString()
    }
}
