package com.example.autolaunch.utils

import android.content.Context
import android.text.format.DateUtils
import com.example.autolaunch.R
import java.text.SimpleDateFormat
import java.util.*

/**
 * 時間格式化工具類
 * 提供用戶友好的時間顯示格式
 */
object TimeFormatUtils {
    
    /**
     * 格式化相對時間（用於上次執行時間）
     * @param context 上下文
     * @param timestamp Unix 時間戳
     * @return 格式化的相對時間字串，如 "5 分鐘前"、"1 小時前"、"昨天 10:30"
     */
    fun formatRelativeTime(context: Context, timestamp: Long?): String {
        if (timestamp == null || timestamp == 0L) {
            return "從未執行"
        }
        
        val now = System.currentTimeMillis()
        val diff = now - timestamp
        
        return when {
            diff < DateUtils.MINUTE_IN_MILLIS -> context.getString(R.string.time_just_now)
            diff < DateUtils.HOUR_IN_MILLIS -> context.getString(R.string.time_minutes_ago, diff / DateUtils.MINUTE_IN_MILLIS)
            diff < DateUtils.DAY_IN_MILLIS -> {
                val hours = diff / DateUtils.HOUR_IN_MILLIS
                if (hours == 1L) context.getString(R.string.time_hour_ago) else context.getString(R.string.time_hours_ago, hours)
            }
            diff < 2 * DateUtils.DAY_IN_MILLIS -> {
                val timeFormat = SimpleDateFormat("HH:mm", Locale.getDefault())
                context.getString(R.string.time_yesterday, timeFormat.format(Date(timestamp)))
            }
            diff < DateUtils.WEEK_IN_MILLIS -> {
                val days = diff / DateUtils.DAY_IN_MILLIS
                context.getString(R.string.time_days_ago, days)
            }
            else -> {
                val dateFormat = SimpleDateFormat("MM/dd HH:mm", Locale.getDefault())
                dateFormat.format(Date(timestamp))
            }
        }
    }
    
    /**
     * 格式化未來時間（用於下次執行時間）
     * @param context 上下文
     * @param timestamp Unix 時間戳
     * @return 格式化的未來時間字串，如 "5小時後"、"1天後"、"3天後"
     */
    fun formatFutureTime(context: Context, timestamp: Long?): String {
        if (timestamp == null || timestamp == 0L) {
            return ""
        }

        val now = System.currentTimeMillis()
        val diff = timestamp - now

        if (diff <= 0) {
            return context.getString(R.string.time_expired)
        }

        return when {
            diff < DateUtils.HOUR_IN_MILLIS -> {
                val minutes = (diff / DateUtils.MINUTE_IN_MILLIS).toInt()
                if (minutes <= 1) context.getString(R.string.time_minute_later) else context.getString(R.string.time_minutes_later, minutes)
            }
            diff < DateUtils.DAY_IN_MILLIS -> {
                val hours = (diff / DateUtils.HOUR_IN_MILLIS).toInt()
                if (hours == 1) context.getString(R.string.time_hour_later) else context.getString(R.string.time_hours_later, hours)
            }
            diff < DateUtils.WEEK_IN_MILLIS -> {
                val days = (diff / DateUtils.DAY_IN_MILLIS).toInt()
                if (days == 1) context.getString(R.string.time_day_later) else context.getString(R.string.time_days_later, days)
            }
            else -> {
                val weeks = (diff / DateUtils.WEEK_IN_MILLIS).toInt()
                if (weeks == 1) context.getString(R.string.time_week_later) else context.getString(R.string.time_weeks_later, weeks)
            }
        }
    }
    
    /**
     * 判斷兩個 Calendar 是否為同一天
     */
    private fun isSameDay(cal1: Calendar, cal2: Calendar): Boolean {
        return cal1.get(Calendar.YEAR) == cal2.get(Calendar.YEAR) &&
                cal1.get(Calendar.DAY_OF_YEAR) == cal2.get(Calendar.DAY_OF_YEAR)
    }
    
    /**
     * 判斷 targetCalendar 是否為 currentCalendar 的明天
     */
    private fun isTomorrow(currentCalendar: Calendar, targetCalendar: Calendar): Boolean {
        val tomorrow = currentCalendar.clone() as Calendar
        tomorrow.add(Calendar.DAY_OF_YEAR, 1)
        return isSameDay(tomorrow, targetCalendar)
    }
    
    /**
     * 獲取星期幾的中文字串
     */
    private fun getDayOfWeekString(dayOfWeek: Int): String {
        return when (dayOfWeek) {
            Calendar.SUNDAY -> "週日"
            Calendar.MONDAY -> "週一"
            Calendar.TUESDAY -> "週二"
            Calendar.WEDNESDAY -> "週三"
            Calendar.THURSDAY -> "週四"
            Calendar.FRIDAY -> "週五"
            Calendar.SATURDAY -> "週六"
            else -> ""
        }
    }
} 