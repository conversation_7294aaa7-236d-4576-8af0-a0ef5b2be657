package com.example.autolaunch.utils

import android.content.Context
import android.content.pm.PackageManager
import android.os.Build
import android.os.Environment
import android.util.Log
import com.example.autolaunch.model.*
import com.example.autolaunch.utils.ResourceManager.safeUse
import com.google.gson.Gson
import com.google.gson.GsonBuilder
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlinx.coroutines.flow.first
import java.io.File
import java.io.FileReader
import java.io.FileWriter
import java.io.IOException
import java.text.SimpleDateFormat
import java.util.*

/**
 * 備份管理器
 * 負責排程數據的備份和恢復操作
 */
class BackupManager(private val context: Context) {
    
    companion object {
        private const val TAG = "BackupManager"
        private const val BACKUP_DIRECTORY = "AutoLaunch"
        private const val BACKUP_FILE_EXTENSION = ".json"
        private const val MAX_BACKUP_FILES = 10 // 最多保留10個備份文件
    }
    
    val gson: Gson = GsonBuilder()
        .setPrettyPrinting()
        .create()
    
    private val repository = ScheduleRepository(context)
    
    /**
     * 創建本地備份
     */
    suspend fun createLocalBackup(): BackupResult = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Starting local backup creation")
            
            // 獲取所有排程數據
            val schedules = repository.getAllSchedules().first()
            if (schedules.isEmpty()) {
                return@withContext BackupResult(
                    success = false,
                    message = "沒有排程數據可以備份"
                )
            }
            
            // 創建備份數據
            val backupData = createBackupData(schedules)
            
            // 序列化為JSON
            val jsonString = gson.toJson(backupData)
            
            // 保存到文件
            val backupFile = createBackupFile(backupData.getSuggestedFileName())
            FileWriter(backupFile).safeUse { writer ->
                writer.write(jsonString)
            }
            
            // 清理舊的備份文件
            cleanupOldBackups()
            
            Log.d(TAG, "Local backup created successfully: ${backupFile.absolutePath}")
            
            BackupResult(
                success = true,
                message = "備份創建成功",
                filePath = backupFile.absolutePath,
                fileSize = backupFile.length(),
                scheduleCount = schedules.size
            )
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to create local backup", e)
            BackupResult(
                success = false,
                message = "備份創建失敗：${e.message}",
                error = e
            )
        }
    }
    
    /**
     * 從本地文件恢復備份
     */
    suspend fun restoreFromLocalFile(filePath: String): RestoreResult = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Starting restore from local file: $filePath")
            
            val file = File(filePath)
            if (!file.exists()) {
                return@withContext RestoreResult(
                    success = false,
                    message = "備份文件不存在"
                )
            }
            
            // 讀取並解析JSON
            val jsonString = FileReader(file).use { it.readText() }
            val backupData = gson.fromJson(jsonString, BackupData::class.java)
            
            // 驗證備份數據
            if (!backupData.isValid()) {
                return@withContext RestoreResult(
                    success = false,
                    message = "備份文件格式無效或數據損壞"
                )
            }
            
            // 執行恢復操作
            restoreSchedules(backupData)
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to restore from local file", e)
            RestoreResult(
                success = false,
                message = "恢復失敗：${e.message}",
                errorCount = 1,
                errors = listOf(e.message ?: "未知錯誤")
            )
        }
    }
    
    /**
     * 從JSON字符串恢復備份
     */
    suspend fun restoreFromJson(jsonString: String): RestoreResult = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Starting restore from JSON string")
            
            // 解析JSON
            val backupData = gson.fromJson(jsonString, BackupData::class.java)
            
            // 驗證備份數據
            if (!backupData.isValid()) {
                return@withContext RestoreResult(
                    success = false,
                    message = "備份數據格式無效或數據損壞"
                )
            }
            
            // 執行恢復操作
            restoreSchedules(backupData)
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to restore from JSON", e)
            RestoreResult(
                success = false,
                message = "恢復失敗：${e.message}",
                errorCount = 1,
                errors = listOf(e.message ?: "未知錯誤")
            )
        }
    }
    
    /**
     * 從Uri恢復備份
     */
    suspend fun restoreFromUri(context: Context, uri: android.net.Uri): RestoreResult = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Starting restore from URI: $uri")

            // 從Uri讀取內容
            val jsonString = FileUtils.readTextFromUri(context, uri)
            if (jsonString.isNullOrEmpty()) {
                return@withContext RestoreResult(
                    success = false,
                    message = "無法讀取備份文件內容"
                )
            }

            // 執行恢復
            restoreFromJson(jsonString)

        } catch (e: Exception) {
            Log.e(TAG, "Failed to restore from URI", e)
            RestoreResult(
                success = false,
                message = "從文件恢復失敗：${e.message}",
                errorCount = 1,
                errors = listOf(e.message ?: "未知錯誤")
            )
        }
    }

    /**
     * 將備份保存到Uri
     */
    suspend fun saveBackupToUri(context: Context, uri: android.net.Uri): BackupResult = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Starting backup save to URI: $uri")

            // 獲取所有排程數據
            val schedules = repository.getAllSchedules().first()
            if (schedules.isEmpty()) {
                return@withContext BackupResult(
                    success = false,
                    message = "沒有排程數據可以備份"
                )
            }

            // 創建備份數據
            val backupData = createBackupData(schedules)

            // 序列化為JSON
            val jsonString = gson.toJson(backupData)

            // 寫入Uri
            val success = FileUtils.writeTextToUri(context, uri, jsonString)
            if (!success) {
                return@withContext BackupResult(
                    success = false,
                    message = "無法寫入備份文件"
                )
            }

            Log.d(TAG, "Backup saved to URI successfully")

            BackupResult(
                success = true,
                message = "備份保存成功",
                filePath = uri.toString(),
                fileSize = jsonString.toByteArray().size.toLong(),
                scheduleCount = schedules.size
            )

        } catch (e: Exception) {
            Log.e(TAG, "Failed to save backup to URI", e)
            BackupResult(
                success = false,
                message = "保存備份失敗：${e.message}",
                error = e
            )
        }
    }

    /**
     * 獲取本地備份文件列表
     */
    suspend fun getLocalBackupFiles(): List<File> = withContext(Dispatchers.IO) {
        try {
            val backupDir = getBackupDirectory()
            if (!backupDir.exists()) {
                return@withContext emptyList()
            }

            backupDir.listFiles { file ->
                file.isFile && file.name.endsWith(BACKUP_FILE_EXTENSION)
            }?.sortedByDescending { it.lastModified() } ?: emptyList()

        } catch (e: Exception) {
            Log.e(TAG, "Failed to get local backup files", e)
            emptyList()
        }
    }

    /**
     * 刪除本地備份文件
     */
    suspend fun deleteLocalBackupFile(file: File): Boolean = withContext(Dispatchers.IO) {
        try {
            val success = file.delete()
            if (success) {
                Log.d(TAG, "Deleted backup file: ${file.name}")
            }
            success
        } catch (e: Exception) {
            Log.e(TAG, "Failed to delete backup file: ${file.absolutePath}", e)
            false
        }
    }

    /**
     * 獲取備份文件信息
     */
    suspend fun getBackupFileInfo(file: File): BackupFileInfo? = withContext(Dispatchers.IO) {
        try {
            if (!file.exists() || !file.isFile) {
                return@withContext null
            }

            val jsonString = FileReader(file).safeUse { reader ->
                reader.readText()
            } ?: return@withContext null

            val backupData = gson.fromJson(jsonString, BackupData::class.java)

            BackupFileInfo(
                fileName = file.name,
                filePath = file.absolutePath,
                fileSize = file.length(),
                createdTime = backupData.timestamp,
                scheduleCount = backupData.totalSchedules,
                appVersion = backupData.deviceInfo.appVersion,
                deviceModel = backupData.deviceInfo.deviceModel,
                isValid = backupData.isValid()
            )

        } catch (e: Exception) {
            Log.e(TAG, "Failed to get backup file info: ${file.absolutePath}", e)
            null
        }
    }
    
    /**
     * 創建備份數據對象
     */
    fun createBackupData(schedules: List<Schedule>): BackupData {
        val deviceInfo = DeviceInfo(
            appVersion = getAppVersion(),
            androidVersion = Build.VERSION.RELEASE,
            deviceModel = Build.MODEL,
            deviceManufacturer = Build.MANUFACTURER
        )
        
        val scheduleBackups = schedules.map { ScheduleBackup.fromSchedule(it) }
        
        return BackupData(
            deviceInfo = deviceInfo,
            schedules = scheduleBackups
        )
    }
    
    /**
     * 執行排程恢復操作
     */
    private suspend fun restoreSchedules(backupData: BackupData): RestoreResult {
        var importedCount = 0
        var skippedCount = 0
        var errorCount = 0
        val errors = mutableListOf<String>()

        try {
            // 先清空所有現有排程（覆蓋模式）
            val deletedCount = repository.deleteAllSchedules()
            Log.d(TAG, "Cleared $deletedCount existing schedules before restore")

            for (scheduleBackup in backupData.schedules) {
                try {
                    if (!scheduleBackup.isValid()) {
                        skippedCount++
                        errors.add("跳過無效排程：${scheduleBackup.taskName ?: scheduleBackup.appName}")
                        continue
                    }

                    val schedule = scheduleBackup.toSchedule()
                    repository.insertSchedule(schedule)
                    importedCount++

                } catch (e: Exception) {
                    errorCount++
                    errors.add("匯入排程失敗：${scheduleBackup.taskName ?: scheduleBackup.appName} - ${e.message}")
                    Log.e(TAG, "Failed to import schedule", e)
                }
            }

        } catch (e: Exception) {
            Log.e(TAG, "Failed to clear existing schedules", e)
            errors.add("清空現有排程失敗：${e.message}")
            errorCount++
        }

        Log.d(TAG, "Restore completed: imported=$importedCount, skipped=$skippedCount, errors=$errorCount")

        return RestoreResult(
            success = importedCount > 0,
            message = if (importedCount > 0) {
                "成功覆蓋匯入 $importedCount 個排程" +
                if (skippedCount > 0) "，跳過 $skippedCount 個" else "" +
                if (errorCount > 0) "，失敗 $errorCount 個" else ""
            } else {
                "沒有成功匯入任何排程"
            },
            importedCount = importedCount,
            skippedCount = skippedCount,
            errorCount = errorCount,
            errors = errors
        )
    }
    
    /**
     * 創建備份文件
     */
    private fun createBackupFile(fileName: String): File {
        val backupDir = getBackupDirectory()
        if (!backupDir.exists()) {
            backupDir.mkdirs()
        }
        return File(backupDir, fileName)
    }
    
    /**
     * 獲取備份目錄
     */
    private fun getBackupDirectory(): File {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            // Android 10+ 使用應用專用目錄
            File(context.getExternalFilesDir(Environment.DIRECTORY_DOWNLOADS), BACKUP_DIRECTORY)
        } else {
            // Android 9 及以下使用公共 Downloads 目錄
            File(
                Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS),
                BACKUP_DIRECTORY
            )
        }
    }
    
    /**
     * 清理舊的備份文件
     */
    private fun cleanupOldBackups() {
        try {
            val backupFiles = getBackupDirectory().listFiles { file ->
                file.isFile && file.name.endsWith(BACKUP_FILE_EXTENSION)
            }?.sortedByDescending { it.lastModified() }
            
            if (backupFiles != null && backupFiles.size > MAX_BACKUP_FILES) {
                for (i in MAX_BACKUP_FILES until backupFiles.size) {
                    backupFiles[i].delete()
                    Log.d(TAG, "Deleted old backup file: ${backupFiles[i].name}")
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to cleanup old backups", e)
        }
    }
    
    /**
     * 獲取應用版本
     */
    private fun getAppVersion(): String {
        return try {
            val pInfo = context.packageManager.getPackageInfo(context.packageName, 0)
            pInfo.versionName ?: "1.0.0"
        } catch (e: PackageManager.NameNotFoundException) {
            "1.0.0"
        }
    }
}
