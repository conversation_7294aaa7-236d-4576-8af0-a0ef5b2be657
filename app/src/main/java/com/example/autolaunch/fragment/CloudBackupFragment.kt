package com.example.autolaunch.fragment

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import com.example.autolaunch.R
import com.example.autolaunch.databinding.FragmentCloudBackupBinding
import com.example.autolaunch.dialog.BackupFileListDialog
import com.example.autolaunch.utils.CloudBackupManager
import com.google.android.gms.auth.api.signin.GoogleSignIn
import com.google.android.gms.auth.api.signin.GoogleSignInAccount
import com.google.android.gms.auth.api.signin.GoogleSignInStatusCodes
import com.google.android.gms.common.api.ApiException
import kotlinx.coroutines.launch

/**
 * 雲端備份Fragment
 */
class CloudBackupFragment : Fragment() {
    
    companion object {
        private const val TAG = "CloudBackupFragment"
        
        fun newInstance(): CloudBackupFragment {
            return CloudBackupFragment()
        }
    }
    
    private var _binding: FragmentCloudBackupBinding? = null
    private val binding get() = _binding!!

    private lateinit var cloudBackupManager: CloudBackupManager

    // Google 登錄結果處理器
    private val googleSignInLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        Log.d(TAG, "Google sign-in result: resultCode=${result.resultCode}, data=${result.data}")

        if (result.resultCode == Activity.RESULT_OK) {
            val task = GoogleSignIn.getSignedInAccountFromIntent(result.data)
            try {
                val account = task.getResult(ApiException::class.java)
                Log.d(TAG, "Google sign-in successful: ${account.email}")
                handleSignInSuccess(account)
            } catch (e: ApiException) {
                Log.e(TAG, "Google sign-in failed with ApiException", e)
                handleSignInFailure(e)
            }
        } else if (result.resultCode == Activity.RESULT_CANCELED) {
            Log.d(TAG, "Google sign-in was cancelled by user")
            Toast.makeText(requireContext(), getString(R.string.google_login_cancelled), Toast.LENGTH_SHORT).show()
        } else {
            Log.w(TAG, "Google sign-in returned unexpected result code: ${result.resultCode}")
            Toast.makeText(requireContext(), "Google 登入異常，請重試", Toast.LENGTH_SHORT).show()
        }
    }
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentCloudBackupBinding.inflate(inflater, container, false)
        return binding.root
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        cloudBackupManager = CloudBackupManager(requireContext())
        
        setupUI()
        updateGoogleAccountStatus()
    }
    
    override fun onResume() {
        super.onResume()
        updateGoogleAccountStatus()
    }
    
    private fun setupUI() {
        // Google 登錄按鈕
        binding.btnGoogleSignIn.setOnClickListener {
            handleGoogleSignInClick()
        }
        
        // 創建雲端備份按鈕
        binding.btnCreateCloudBackup.setOnClickListener {
            createCloudBackup()
        }
        
        // 同步備份按鈕
        binding.btnSyncBackup.setOnClickListener {
            syncBackup()
        }
        
        // 從雲端匯入按鈕
        binding.btnImportFromCloud.setOnClickListener {
            importFromCloud()
        }
        
        // 管理雲端備份按鈕
        binding.btnManageCloudBackups.setOnClickListener {
            manageCloudBackups()
        }
    }
    
    private fun updateGoogleAccountStatus() {
        val account = GoogleSignIn.getLastSignedInAccount(requireContext())
        val isSignedIn = account != null
        
        if (isSignedIn && account != null) {
            binding.tvGoogleAccountStatus.text = "已登錄：${account.email}"
            binding.btnGoogleSignIn.text = "登出"
            binding.btnGoogleSignIn.setIconResource(R.drawable.ic_close)
            
            // 啟用雲端相關按鈕
            binding.btnCreateCloudBackup.isEnabled = true
            binding.btnImportFromCloud.isEnabled = true
            binding.btnManageCloudBackups.isEnabled = true
            binding.btnSyncBackup.isEnabled = true
        } else {
            binding.tvGoogleAccountStatus.text = "未登錄 Google 帳戶"
            binding.btnGoogleSignIn.text = "登錄 Google 帳戶"
            binding.btnGoogleSignIn.setIconResource(R.drawable.ic_login_24)
            
            // 禁用雲端相關按鈕
            binding.btnCreateCloudBackup.isEnabled = false
            binding.btnImportFromCloud.isEnabled = false
            binding.btnManageCloudBackups.isEnabled = false
            binding.btnSyncBackup.isEnabled = false
        }
    }
    
    private fun handleGoogleSignInClick() {
        val account = GoogleSignIn.getLastSignedInAccount(requireContext())
        if (account != null) {
            // 已登錄，執行登出
            signOutGoogle()
        } else {
            // 未登錄，執行登錄
            signInGoogle()
        }
    }
    
    private fun signInGoogle() {
        try {
            val signInClient = cloudBackupManager.getGoogleSignInClient()
            val signInIntent = signInClient.signInIntent
            googleSignInLauncher.launch(signInIntent)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to start Google sign in", e)
            Toast.makeText(requireContext(), "啟動Google登錄失敗：${e.message}", Toast.LENGTH_LONG).show()
        }
    }

    private fun handleSignInSuccess(account: GoogleSignInAccount) {
        Log.d(TAG, "Handling successful Google sign-in for: ${account.email}")

        // 檢查 Google Drive API 配置狀態
        lifecycleScope.launch {
            try {
                showProgress(true)
                val driveInitialized = cloudBackupManager.initializeGoogleDrive()

                if (driveInitialized) {
                    Toast.makeText(requireContext(), "Google 帳戶登錄成功", Toast.LENGTH_SHORT).show()
                } else {
                    // Google Drive API 未完全配置，但登入成功
                    Toast.makeText(
                        requireContext(),
                        "Google 帳戶登錄成功\n注意：雲端備份功能需要完整的 API 配置",
                        Toast.LENGTH_LONG
                    ).show()
                }

                updateGoogleAccountStatus()

            } catch (e: Exception) {
                Log.e(TAG, "Error during Google Drive initialization", e)
                Toast.makeText(
                    requireContext(),
                    "Google 帳戶登錄成功，但雲端服務初始化失敗",
                    Toast.LENGTH_LONG
                ).show()
                updateGoogleAccountStatus()
            } finally {
                showProgress(false)
            }
        }
    }

    private fun handleSignInFailure(exception: ApiException) {
        Log.e(TAG, "Google sign in failed with code: ${exception.statusCode}", exception)

        val errorMessage = when (exception.statusCode) {
            GoogleSignInStatusCodes.SIGN_IN_CANCELLED -> "登入已取消"
            GoogleSignInStatusCodes.SIGN_IN_FAILED -> "登入失敗，請檢查網路連線"
            GoogleSignInStatusCodes.SIGN_IN_REQUIRED -> "需要重新登入"
            GoogleSignInStatusCodes.INVALID_ACCOUNT -> "無效的帳戶"
            GoogleSignInStatusCodes.NETWORK_ERROR -> "網路連線錯誤"
            else -> "登入失敗：${exception.message}"
        }

        Toast.makeText(requireContext(), "Google $errorMessage", Toast.LENGTH_LONG).show()
    }
    
    private fun signOutGoogle() {
        lifecycleScope.launch {
            try {
                showProgress(true)
                val success = cloudBackupManager.signOutGoogle()
                if (success) {
                    Toast.makeText(requireContext(), "已登出 Google 帳戶", Toast.LENGTH_SHORT).show()
                    updateGoogleAccountStatus()
                } else {
                    Toast.makeText(requireContext(), "Google 登出失敗", Toast.LENGTH_LONG).show()
                }
            } catch (e: Exception) {
                Log.e(TAG, "Failed to sign out Google", e)
                Toast.makeText(requireContext(), "Google 登出失敗：${e.message}", Toast.LENGTH_LONG).show()
            } finally {
                showProgress(false)
            }
        }
    }
    
    private fun createCloudBackup() {
        if (!cloudBackupManager.isGoogleSignedIn()) {
            Toast.makeText(requireContext(), "請先登入 Google 帳戶", Toast.LENGTH_SHORT).show()
            return
        }

        lifecycleScope.launch {
            try {
                showProgress(true)

                // 先檢查 Google Drive 是否可用
                val driveInitialized = cloudBackupManager.initializeGoogleDrive()
                if (!driveInitialized) {
                    Toast.makeText(
                        requireContext(),
                        "雲端備份功能需要完整的 Google Drive API 配置\n\n" +
                        "建議使用本地備份功能",
                        Toast.LENGTH_LONG
                    ).show()
                    return@launch
                }

                val result = cloudBackupManager.createCloudBackup()
                if (result.success) {
                    Toast.makeText(
                        requireContext(),
                        "雲端備份創建成功\n已備份 ${result.scheduleCount} 個排程",
                        Toast.LENGTH_LONG
                    ).show()
                } else {
                    Toast.makeText(requireContext(), result.message, Toast.LENGTH_LONG).show()
                }
            } catch (e: Exception) {
                Log.e(TAG, "Failed to create cloud backup", e)
                Toast.makeText(requireContext(), "創建雲端備份失敗：${e.message}", Toast.LENGTH_LONG).show()
            } finally {
                showProgress(false)
            }
        }
    }
    
    private fun syncBackup() {
        if (!cloudBackupManager.isGoogleSignedIn()) {
            Toast.makeText(requireContext(), "請先登入 Google 帳戶", Toast.LENGTH_SHORT).show()
            return
        }

        lifecycleScope.launch {
            try {
                showProgress(true)

                // 先檢查 Google Drive 是否可用
                val driveInitialized = cloudBackupManager.initializeGoogleDrive()
                if (!driveInitialized) {
                    Toast.makeText(
                        requireContext(),
                        "雲端同步功能需要完整的 Google Drive API 配置\n\n" +
                        "建議使用本地備份功能",
                        Toast.LENGTH_LONG
                    ).show()
                    return@launch
                }

                val result = cloudBackupManager.syncBackup()
                Toast.makeText(requireContext(), result.getMessage(), Toast.LENGTH_LONG).show()
            } catch (e: Exception) {
                Log.e(TAG, "Failed to sync backup", e)
                Toast.makeText(requireContext(), "同步備份失敗：${e.message}", Toast.LENGTH_LONG).show()
            } finally {
                showProgress(false)
            }
        }
    }
    
    private fun importFromCloud() {
        if (!cloudBackupManager.isGoogleSignedIn()) {
            Toast.makeText(requireContext(), "請先登入 Google 帳戶", Toast.LENGTH_SHORT).show()
            return
        }

        lifecycleScope.launch {
            try {
                showProgress(true)

                // 先檢查 Google Drive 是否可用
                val driveInitialized = cloudBackupManager.initializeGoogleDrive()
                if (!driveInitialized) {
                    Toast.makeText(
                        requireContext(),
                        "雲端匯入功能需要完整的 Google Drive API 配置\n\n" +
                        "建議使用本地備份功能",
                        Toast.LENGTH_LONG
                    ).show()
                    return@launch
                }

                val files = cloudBackupManager.getCloudBackupFiles()
                if (files.isEmpty()) {
                    Toast.makeText(requireContext(), "沒有找到雲端備份文件", Toast.LENGTH_SHORT).show()
                    return@launch
                }

                val dialog = BackupFileListDialog.showCloudBackupFiles(
                    requireContext(),
                    files,
                    onFileSelected = { file ->
                        restoreFromCloudFile(file.id)
                    },
                    onFileDeleted = { file ->
                        deleteCloudBackupFile(file)
                    }
                )
                dialog.show()

            } catch (e: Exception) {
                Log.e(TAG, "Failed to load cloud backup files", e)
                Toast.makeText(requireContext(), "載入雲端備份文件失敗：${e.message}", Toast.LENGTH_LONG).show()
            } finally {
                showProgress(false)
            }
        }
    }
    
    private fun manageCloudBackups() {
        lifecycleScope.launch {
            try {
                showProgress(true)
                val files = cloudBackupManager.getCloudBackupFiles()
                if (files.isEmpty()) {
                    Toast.makeText(requireContext(), "沒有找到雲端備份文件", Toast.LENGTH_SHORT).show()
                    return@launch
                }

                val dialog = BackupFileListDialog.showCloudBackupFiles(
                    requireContext(),
                    files,
                    onFileSelected = { file ->
                        // 管理模式下不執行恢復，只顯示信息
                        Toast.makeText(requireContext(), "文件：${file.name}", Toast.LENGTH_SHORT).show()
                    },
                    onFileDeleted = { file ->
                        deleteCloudBackupFile(file)
                    }
                )
                dialog.show()

            } catch (e: Exception) {
                Log.e(TAG, "Failed to load cloud backup files", e)
                Toast.makeText(requireContext(), "載入雲端備份文件失敗：${e.message}", Toast.LENGTH_LONG).show()
            } finally {
                showProgress(false)
            }
        }
    }
    
    private fun restoreFromCloudFile(fileId: String) {
        lifecycleScope.launch {
            try {
                showProgress(true)
                val result = cloudBackupManager.restoreFromCloudFile(fileId)
                if (result.success) {
                    Toast.makeText(
                        requireContext(),
                        "從雲端恢復成功\n匯入：${result.importedCount} 個排程\n跳過：${result.skippedCount} 個\n失敗：${result.errorCount} 個",
                        Toast.LENGTH_LONG
                    ).show()
                } else {
                    Toast.makeText(requireContext(), result.message, Toast.LENGTH_LONG).show()
                }
            } catch (e: Exception) {
                Log.e(TAG, "Failed to restore from cloud file", e)
                Toast.makeText(requireContext(), "從雲端恢復失敗：${e.message}", Toast.LENGTH_LONG).show()
            } finally {
                showProgress(false)
            }
        }
    }
    
    private fun deleteCloudBackupFile(file: com.example.autolaunch.utils.DriveFileInfo) {
        lifecycleScope.launch {
            try {
                showProgress(true)
                val result = cloudBackupManager.deleteCloudBackupFile(file.id)
                if (result.success) {
                    Toast.makeText(requireContext(), "雲端備份文件已刪除", Toast.LENGTH_SHORT).show()
                } else {
                    Toast.makeText(requireContext(), result.message, Toast.LENGTH_LONG).show()
                }
            } catch (e: Exception) {
                Log.e(TAG, "Failed to delete cloud backup file", e)
                Toast.makeText(requireContext(), "刪除雲端備份文件失敗：${e.message}", Toast.LENGTH_LONG).show()
            } finally {
                showProgress(false)
            }
        }
    }
    
    private fun showProgress(show: Boolean) {
        // 通知父Activity顯示/隱藏進度條
        (activity as? com.example.autolaunch.BackupRestoreActivity)?.showProgress(show)
    }
    
    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
