package com.example.autolaunch.utils

import android.content.Context
import android.util.Log
import com.example.autolaunch.model.*
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.flow.first
import java.text.SimpleDateFormat
import java.util.*

/**
 * 系統日誌管理器
 * 負責記錄和管理應用程式的各種操作日誌
 */
object SystemLogManager {

    private const val TAG = "SystemLogManager"
    private const val LOG_RETENTION_DAYS = 7 // 日誌保留天數
    private const val MAX_LOG_MESSAGE_LENGTH = 500 // 最大日誌訊息長度
    private const val MAX_LOG_DETAILS_LENGTH = 2000 // 最大日誌詳細資訊長度

    /**
     * 記錄排程建立日誌
     */
    fun logScheduleCreated(
        context: Context,
        schedule: Schedule,
        details: String? = null
    ) {
        val scheduleName = getScheduleDisplayName(schedule)
        val message = context.getString(com.example.autolaunch.R.string.log_schedule_created, scheduleName)

        logEvent(
            context = context,
            logType = LogType.SUCCESS,
            actionType = ActionType.SCHEDULE_CREATED,
            scheduleId = schedule.id,
            scheduleName = scheduleName,
            message = message,
            details = details
        )
    }
    
    /**
     * 記錄排程修改日志
     */
    fun logScheduleUpdated(
        context: Context,
        schedule: Schedule,
        details: String? = null
    ) {
        val scheduleName = getScheduleDisplayName(schedule)
        val message = context.getString(com.example.autolaunch.R.string.log_schedule_updated, scheduleName)

        logEvent(
            context = context,
            logType = LogType.INFO,
            actionType = ActionType.SCHEDULE_UPDATED,
            scheduleId = schedule.id,
            scheduleName = scheduleName,
            message = message,
            details = details
        )
    }
    
    /**
     * 記錄排程刪除日志
     */
    fun logScheduleDeleted(
        context: Context,
        schedule: Schedule,
        details: String? = null
    ) {
        val scheduleName = getScheduleDisplayName(schedule)
        val message = context.getString(com.example.autolaunch.R.string.log_schedule_deleted, scheduleName)

        logEvent(
            context = context,
            logType = LogType.WARNING,
            actionType = ActionType.SCHEDULE_DELETED,
            scheduleId = schedule.id,
            scheduleName = scheduleName,
            message = message,
            details = details
        )
    }
    
    /**
     * 記錄排程執行日志
     */
    fun logScheduleExecuted(
        context: Context,
        scheduleId: Long,
        scheduleName: String,
        success: Boolean = true,
        details: String? = null
    ) {
        val message = if (success) {
            context.getString(com.example.autolaunch.R.string.log_schedule_executed, scheduleName)
        } else {
            context.getString(com.example.autolaunch.R.string.log_schedule_execution_failed, scheduleName)
        }

        logEvent(
            context = context,
            logType = if (success) LogType.SUCCESS else LogType.ERROR,
            actionType = ActionType.SCHEDULE_EXECUTED,
            scheduleId = scheduleId,
            scheduleName = scheduleName,
            message = message,
            details = details
        )
    }
    
    /**
     * 記錄排程啟用/停用日誌
     */
    fun logScheduleEnabledChanged(
        context: Context,
        schedule: Schedule,
        enabled: Boolean,
        details: String? = null
    ) {
        val scheduleName = getScheduleDisplayName(schedule)
        val action = if (enabled) "啟用" else "停用"
        val message = "$action 排程：$scheduleName"
        
        logEvent(
            context = context,
            logType = LogType.INFO,
            actionType = if (enabled) ActionType.SCHEDULE_ENABLED else ActionType.SCHEDULE_DISABLED,
            scheduleId = schedule.id,
            scheduleName = scheduleName,
            message = message,
            details = details
        )
    }
    
    /**
     * 記錄應用啟動日誌
     */
    fun logAppStarted(
        context: Context,
        details: String? = null
    ) {
        logEvent(
            context = context,
            logType = LogType.INFO,
            actionType = ActionType.APP_STARTED,
            message = "應用程式啟動",
            details = details
        )
    }
    
    /**
     * 記錄權限相關日志
     */
    fun logPermission(
        context: Context,
        permissionName: String,
        granted: Boolean,
        details: String? = null
    ) {
        val action = if (granted) "授予" else "拒絕"
        val message = "$action 權限：$permissionName"
        
        logEvent(
            context = context,
            logType = if (granted) LogType.SUCCESS else LogType.WARNING,
            actionType = if (granted) ActionType.PERMISSION_GRANTED else ActionType.PERMISSION_DENIED,
            message = message,
            details = details
        )
    }
    
    /**
     * 記錄系統錯誤日志
     */
    fun logSystemError(
        context: Context,
        errorMessage: String,
        exception: Throwable? = null,
        details: String? = null
    ) {
        val fullDetails = if (exception != null) {
            "${details ?: ""}\n錯誤詳情：${exception.message}\n堆疊追蹤：${Log.getStackTraceString(exception)}"
        } else {
            details
        }
        
        logEvent(
            context = context,
            logType = LogType.ERROR,
            actionType = ActionType.SYSTEM_ERROR,
            message = context.getString(com.example.autolaunch.R.string.log_system_error, errorMessage),
            details = fullDetails
        )
    }
    
    /**
     * 記錄通用事件日誌
     */
    private fun logEvent(
        context: Context,
        logType: LogType,
        actionType: ActionType,
        scheduleId: Long? = null,
        scheduleName: String? = null,
        message: String,
        details: String? = null
    ) {
        // 驗證輸入參數
        if (message.isBlank()) {
            Log.w(TAG, "Attempted to log empty message")
            return
        }

        CoroutineScope(Dispatchers.IO).launch {
            try {
                val database = AppDatabase.getDatabase(context)
                val systemLogDao = database.systemLogDao()

                // 清理和驗證輸入數據
                val cleanedMessage = message.trim().take(MAX_LOG_MESSAGE_LENGTH)
                val cleanedScheduleName = scheduleName?.trim()?.take(100)
                val cleanedDetails = details?.trim()?.take(MAX_LOG_DETAILS_LENGTH)

                val systemLog = SystemLog(
                    logType = logType.value,
                    actionType = actionType.value,
                    scheduleId = scheduleId,
                    scheduleName = cleanedScheduleName,
                    message = cleanedMessage,
                    details = cleanedDetails
                )

                val insertedId = systemLogDao.insert(systemLog)
                Log.d(TAG, "Log recorded with ID $insertedId: $cleanedMessage")

                // 定期執行日誌輪轉清理（不是每次都執行）
                if (insertedId % 50 == 0L) { // 每50條日誌執行一次清理
                    performLogRotation(systemLogDao)
                }

            } catch (e: Exception) {
                Log.e(TAG, "Failed to record log: $message", e)
                // 不要在這裡再次調用 logSystemError，避免無限遞迴
            }
        }
    }
    
    /**
     * 執行日誌輪轉，刪除超過保留期的日誌
     */
    private suspend fun performLogRotation(systemLogDao: SystemLogDao) {
        try {
            val cutoffTime = System.currentTimeMillis() - (LOG_RETENTION_DAYS * 24 * 60 * 60 * 1000L)

            // 檢查是否有需要清理的日誌
            val oldLogCount = systemLogDao.getLogCount()
            if (oldLogCount > 1000) { // 只有當日誌數量超過1000時才執行清理
                val deletedCount = systemLogDao.deleteLogsBefore(cutoffTime)

                if (deletedCount > 0) {
                    Log.d(TAG, "Log rotation: deleted $deletedCount old logs (total was $oldLogCount)")
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to perform log rotation", e)
        }
    }
    
    /**
     * 獲取排程的顯示名稱
     */
    private fun getScheduleDisplayName(schedule: Schedule): String {
        return schedule.getDisplayName()
    }
    
    /**
     * 手動清理所有日志
     */
    fun clearAllLogs(context: Context, callback: ((Boolean) -> Unit)? = null) {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                val database = AppDatabase.getDatabase(context)
                val systemLogDao = database.systemLogDao()
                systemLogDao.deleteAllLogs()
                
                Log.d(TAG, "All logs cleared")
                callback?.invoke(true)
            } catch (e: Exception) {
                Log.e(TAG, "Failed to clear logs", e)
                callback?.invoke(false)
            }
        }
    }
    
    /**
     * 獲取日志統計信息
     */
    suspend fun getLogStatistics(context: Context): LogStatistics {
        return try {
            val database = AppDatabase.getDatabase(context)
            val systemLogDao = database.systemLogDao()
            
            val totalCount = systemLogDao.getLogCount()
            val infoCount = systemLogDao.getLogCountByType(LogType.INFO.value)
            val warningCount = systemLogDao.getLogCountByType(LogType.WARNING.value)
            val errorCount = systemLogDao.getLogCountByType(LogType.ERROR.value)
            val successCount = systemLogDao.getLogCountByType(LogType.SUCCESS.value)
            val oldestTimestamp = systemLogDao.getOldestLogTimestamp()
            val latestTimestamp = systemLogDao.getLatestLogTimestamp()
            
            LogStatistics(
                totalCount = totalCount,
                infoCount = infoCount,
                warningCount = warningCount,
                errorCount = errorCount,
                successCount = successCount,
                oldestTimestamp = oldestTimestamp,
                latestTimestamp = latestTimestamp
            )
        } catch (e: Exception) {
            Log.e(TAG, "Failed to get log statistics", e)
            LogStatistics()
        }
    }

    /**
     * 匯出日誌為文字格式（用於除錯）
     */
    suspend fun exportLogsAsText(context: Context, limit: Int = 100): String {
        return try {
            // 驗證參數
            val safeLimit = limit.coerceIn(1, 1000) // 限制匯出數量

            val database = AppDatabase.getDatabase(context)
            val systemLogDao = database.systemLogDao()

            val logs = systemLogDao.getRecentLogs(safeLimit).first()

            if (logs.isEmpty()) {
                return "沒有日誌可以匯出"
            }

            buildString {
                try {
                    appendLine("=== ${context.getString(com.example.autolaunch.R.string.log_export_header)} ===")
                    appendLine(context.getString(com.example.autolaunch.R.string.log_export_time, SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault()).format(Date())))
                    appendLine(context.getString(com.example.autolaunch.R.string.log_export_count, logs.size))
                    appendLine("=".repeat(50))
                    appendLine()

                    logs.forEach { log ->
                        try {
                            appendLine("時間：${log.getFullDateTime()}")
                            appendLine("類型：${log.getLogTypeEnum().displayName}")
                            appendLine("操作：${log.getActionTypeEnum().displayName}")
                            appendLine("消息：${log.message}")
                            if (!log.scheduleName.isNullOrBlank()) {
                                appendLine("排程：${log.scheduleName}")
                            }
                            if (!log.details.isNullOrBlank()) {
                                appendLine("詳情：${log.details}")
                            }
                            appendLine("-".repeat(30))
                            appendLine()
                        } catch (e: Exception) {
                            Log.w(TAG, "Failed to export log ${log.id}", e)
                            appendLine("日誌 ${log.id} 匯出失敗")
                            appendLine("-".repeat(30))
                            appendLine()
                        }
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "Failed to build export string", e)
                    append("匯出過程中發生錯誤：${e.message}")
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to export logs", e)
            "導出日志失敗：${e.message}"
        }
    }

    /**
     * 獲取日誌摘要資訊
     */
    suspend fun getLogSummary(context: Context): String {
        return try {
            val statistics = getLogStatistics(context)

            buildString {
                appendLine("日誌摘要：")
                appendLine("總計：${statistics.totalCount} 條")
                appendLine("成功：${statistics.successCount} 條")
                appendLine("警告：${statistics.warningCount} 條")
                appendLine("錯誤：${statistics.errorCount} 條")
                appendLine("資訊：${statistics.infoCount} 條")

                if (statistics.oldestTimestamp != null && statistics.latestTimestamp != null) {
                    val oldestDate = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()).format(Date(statistics.oldestTimestamp))
                    val latestDate = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()).format(Date(statistics.latestTimestamp))
                    appendLine("時間範圍：$oldestDate 至 $latestDate")
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to get log summary", e)
            "獲取日志摘要失敗：${e.message}"
        }
    }

    /**
     * 檢查儲存空間使用情況
     */
    suspend fun checkStorageUsage(context: Context): StorageInfo {
        return try {
            val database = AppDatabase.getDatabase(context)
            val systemLogDao = database.systemLogDao()

            val totalLogs = systemLogDao.getLogCount()
            val oldestTimestamp = systemLogDao.getOldestLogTimestamp()
            val latestTimestamp = systemLogDao.getLatestLogTimestamp()

            // 估算儲存空間使用量（每條日誌平均約200字節）
            val estimatedSize = totalLogs * 200L

            StorageInfo(
                totalLogs = totalLogs,
                estimatedSizeBytes = estimatedSize,
                oldestLogTimestamp = oldestTimestamp,
                latestLogTimestamp = latestTimestamp,
                retentionDays = LOG_RETENTION_DAYS
            )
        } catch (e: Exception) {
            Log.e(TAG, "Failed to check storage usage", e)
            StorageInfo()
        }
    }

    /**
     * 手動觸發日誌清理
     */
    suspend fun manualCleanup(context: Context, beforeDays: Int = LOG_RETENTION_DAYS): CleanupResult {
        return try {
            val database = AppDatabase.getDatabase(context)
            val systemLogDao = database.systemLogDao()

            val cutoffTime = System.currentTimeMillis() - (beforeDays * 24 * 60 * 60 * 1000L)
            val beforeCount = systemLogDao.getLogCount()
            val deletedCount = systemLogDao.deleteLogsBefore(cutoffTime)
            val afterCount = systemLogDao.getLogCount()

            Log.d(TAG, "Manual cleanup: deleted $deletedCount logs (before: $beforeCount, after: $afterCount)")

            CleanupResult(
                success = true,
                deletedCount = deletedCount,
                remainingCount = afterCount,
                message = "成功清理 $deletedCount 條舊日誌"
            )
        } catch (e: Exception) {
            Log.e(TAG, "Failed to perform manual cleanup", e)
            CleanupResult(
                success = false,
                message = "清理失敗：${e.message}"
            )
        }
    }
}

/**
 * 日志統計信息數據類
 */
data class LogStatistics(
    val totalCount: Int = 0,
    val infoCount: Int = 0,
    val warningCount: Int = 0,
    val errorCount: Int = 0,
    val successCount: Int = 0,
    val oldestTimestamp: Long? = null,
    val latestTimestamp: Long? = null
)

/**
 * 儲存空間資訊數據類
 */
data class StorageInfo(
    val totalLogs: Int = 0,
    val estimatedSizeBytes: Long = 0L,
    val oldestLogTimestamp: Long? = null,
    val latestLogTimestamp: Long? = null,
    val retentionDays: Int = 7
) {
    /**
     * 獲取可讀的檔案大小
     */
    fun getReadableSize(): String {
        return when {
            estimatedSizeBytes < 1024 -> "${estimatedSizeBytes}B"
            estimatedSizeBytes < 1024 * 1024 -> "${estimatedSizeBytes / 1024}KB"
            else -> "${estimatedSizeBytes / (1024 * 1024)}MB"
        }
    }

    /**
     * 獲取日誌時間範圍描述
     */
    fun getTimeRangeDescription(): String {
        return if (oldestLogTimestamp != null && latestLogTimestamp != null) {
            val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
            val oldestDate = dateFormat.format(Date(oldestLogTimestamp))
            val latestDate = dateFormat.format(Date(latestLogTimestamp))
            "$oldestDate 至 $latestDate"
        } else {
            "無日誌記錄"
        }
    }
}

/**
 * 清理結果數據類
 */
data class CleanupResult(
    val success: Boolean = false,
    val deletedCount: Int = 0,
    val remainingCount: Int = 0,
    val message: String = ""
)
